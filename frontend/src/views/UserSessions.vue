<template>
  <PhantomLayout title="My Sessions">
    <div class="p-6">
      <!-- Loading indicator -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div
          class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-phantom-blue"
        />
        <span class="ml-3 text-white/80">Loading sessions...</span>
      </div>

      <!-- Error message -->
      <div
        v-if="message && !isSuccess"
        class="bg-red-500/10 backdrop-blur-sm border border-red-500/30 text-white px-6 py-4 rounded-xl mb-6 flex items-center"
      >
        <!-- Error Icon -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-3 text-red-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
          />
        </svg>
        {{ message }}
      </div>

      <!-- Success message -->
      <div
        v-if="message && isSuccess"
        class="bg-green-500/10 backdrop-blur-sm border border-green-500/30 text-white px-6 py-4 rounded-xl mb-6 flex items-center"
      >
        <!-- Success Icon -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-3 text-green-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M5 13l4 4L19 7"
          />
        </svg>
        {{ message }}
      </div>

      <!-- Search bar -->
      <div
        class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6"
      >
        <div class="w-full md:w-auto flex-1">
          <div class="relative">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search sessions..."
              class="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 pl-10 text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50"
            />
            <div
              class="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50"
            >
              <!-- Search Icon -->
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- No sessions message -->
      <div
        v-if="
          !isLoading &&
          pendingSessions.length === 0 &&
          inProgressSessions.length === 0 &&
          completedSessions.length === 0
        "
        class="text-center py-16"
      >
        <div
          class="w-20 h-20 mx-auto mb-6 rounded-full bg-white/5 flex items-center justify-center"
        >
          <!-- Inbox Icon -->
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-10 w-10 text-white/40"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
            />
          </svg>
        </div>
        <h3 class="text-xl font-medium text-white mb-2">No sessions found</h3>
        <p class="text-white/60 mb-6">
          You don't have any assessment sessions yet.
        </p>
      </div>

      <!-- Pending Sessions Section -->
      <div v-if="pendingSessions.length > 0" class="mb-8">
        <h3 class="text-xl font-medium text-white mb-4">Pending Sessions</h3>
        <div
          class="overflow-x-auto rounded-xl border border-white/10 backdrop-blur-sm"
        >
          <table class="w-full text-left">
            <thead>
              <tr class="border-b border-white/10 bg-white/5">
                <th class="py-4 px-6 text-white/80 font-medium">Assessment</th>
                <th class="py-4 px-6 text-white/80 font-medium">Type</th>
                <th class="py-4 px-6 text-white/80 font-medium">Created</th>
                <th class="py-4 px-6 text-white/80 font-medium">Status</th>
                <th class="py-4 px-6 text-white/80 font-medium">Action</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(session, index) in filteredPendingSessions"
                :key="index"
                :class="index % 2 === 0 ? 'bg-white/[0.03]' : 'bg-transparent'"
                class="border-b border-white/5 hover:bg-white/[0.05] transition-colors duration-150"
              >
                <td class="py-4 px-6 text-white">
                  {{ session.assessment_name }}
                </td>
                <td class="py-4 px-6">
                  <span
                    class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium"
                    :class="
                      getAssessmentTypeClass(session.question_selection_mode)
                    "
                  >
                    {{ getAssessmentType(session.question_selection_mode) }}
                  </span>
                </td>
                <td class="py-4 px-6 text-white/80">
                  {{ formatDate(session.created_at) }}
                </td>
                <td class="py-4 px-6">
                  <span
                    class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-500/10 text-yellow-400 border border-yellow-500/20"
                  >
                    Pending
                  </span>
                </td>
                <td class="py-4 px-6">
                  <button
                    class="btn-phantom-secondary text-xs px-3 py-1.5"
                    @click="startSession(session)"
                  >
                    Start
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- In Progress Sessions Section -->
      <div v-if="inProgressSessions.length > 0" class="mb-8">
        <h3 class="text-xl font-medium text-white mb-4">
          In Progress Sessions
        </h3>
        <div
          class="overflow-x-auto rounded-xl border border-white/10 backdrop-blur-sm"
        >
          <table class="w-full text-left">
            <thead>
              <tr class="border-b border-white/10 bg-white/5">
                <th class="py-4 px-6 text-white/80 font-medium">Assessment</th>
                <th class="py-4 px-6 text-white/80 font-medium">Type</th>
                <th class="py-4 px-6 text-white/80 font-medium">Started</th>
                <th class="py-4 px-6 text-white/80 font-medium">Status</th>
                <th class="py-4 px-6 text-white/80 font-medium">Action</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(session, index) in filteredInProgressSessions"
                :key="`in-progress-${index}`"
                :class="index % 2 === 0 ? 'bg-white/[0.03]' : 'bg-transparent'"
                class="border-b border-white/5 hover:bg-white/[0.05] transition-colors duration-150"
              >
                <td class="py-4 px-6 text-white">
                  {{ session.assessment_name }}
                </td>
                <td class="py-4 px-6">
                  <span
                    class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium"
                    :class="
                      getAssessmentTypeClass(session.question_selection_mode)
                    "
                  >
                    {{ getAssessmentType(session.question_selection_mode) }}
                  </span>
                </td>
                <td class="py-4 px-6 text-white/80">
                  {{ formatDate(session.started_at || session.updated_at) }}
                </td>
                <td class="py-4 px-6">
                  <span
                    class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-500/10 text-blue-400 border border-blue-500/20"
                  >
                    In Progress
                  </span>
                </td>
                <td class="py-4 px-6">
                  <button
                    class="btn-phantom-secondary text-xs px-3 py-1.5"
                    @click="startSession(session)"
                  >
                    Continue
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Completed Sessions Section -->
      <div v-if="completedSessions.length > 0">
        <h3 class="text-xl font-medium text-white mb-4">Completed Sessions</h3>
        <div
          class="overflow-x-auto rounded-xl border border-white/10 backdrop-blur-sm"
        >
          <table class="w-full text-left">
            <thead>
              <tr class="border-b border-white/10 bg-white/5">
                <th class="py-4 px-6 text-white/80 font-medium">Assessment</th>
                <th class="py-4 px-6 text-white/80 font-medium">Type</th>
                <th class="py-4 px-6 text-white/80 font-medium">Completed</th>
                <th class="py-4 px-6 text-white/80 font-medium">Status</th>
                <th class="py-4 px-6 text-white/80 font-medium">Score</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(session, index) in filteredCompletedSessions"
                :key="`completed-${index}`"
                :class="index % 2 === 0 ? 'bg-white/[0.03]' : 'bg-transparent'"
                class="border-b border-white/5"
              >
                <td class="py-4 px-6 text-white">
                  {{ session.assessment_name }}
                </td>
                <td class="py-4 px-6">
                  <span
                    class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium"
                    :class="
                      getAssessmentTypeClass(session.question_selection_mode)
                    "
                  >
                    {{ getAssessmentType(session.question_selection_mode) }}
                  </span>
                </td>
                <td class="py-4 px-6 text-white/80">
                  {{ formatDate(session.completed_at) }}
                </td>
                <td class="py-4 px-6">
                  <span
                    class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-500/10 text-green-400 border border-green-500/20"
                  >
                    Completed
                  </span>
                </td>
                <td class="py-4 px-6">
                  <span class="font-medium text-white">{{
                    session.score || "N/A"
                  }}</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { onMounted } from "vue";
import { useUserSessions } from "@/composables";
import PhantomLayout from "@/components/layout/Layout.vue";
import { debug, warning } from "@/utils/logger";

// Composables
const {
  isLoading,
  message,
  isSuccess,
  searchQuery,
  pendingSessions,
  inProgressSessions,
  completedSessions,
  filteredPendingSessions,
  filteredInProgressSessions,
  filteredCompletedSessions,
  fetchUserSessions,
  startSession,
} = useUserSessions();

// UI Helpers & Formatters
const formatDate = (dateString) => {
  if (!dateString) return "N/A";
  const date = new Date(dateString);
  return date.toLocaleDateString() + " " + date.toLocaleTimeString();
};

const getAssessmentType = (questionSelectionMode) => {
  if (import.meta.env.DEV) {
    debug("getAssessmentType called with:", {
      questionSelectionMode,
      type: typeof questionSelectionMode,
    });
  }
  if (!questionSelectionMode) return "N/A";
  const mode = String(questionSelectionMode).toLowerCase().trim();
  switch (mode) {
    case "dynamic":
      return "Dynamic";
    case "fixed":
      return "Fixed";
    default:
      if (import.meta.env.DEV) {
        warning("Unexpected question_selection_mode value:", {
          mode,
          original: questionSelectionMode,
        });
      }
      return mode.charAt(0).toUpperCase() + mode.slice(1);
  }
};

const getAssessmentTypeClass = (questionSelectionMode) => {
  if (!questionSelectionMode) {
    return "bg-gray-500/10 text-gray-400 border border-gray-500/20";
  }
  const mode = String(questionSelectionMode).toLowerCase().trim();
  switch (mode) {
    case "dynamic":
      return "bg-purple-500/10 text-purple-400 border border-purple-500/20";
    case "fixed":
      return "bg-orange-500/10 text-orange-400 border border-orange-500/20";
    default:
      return "bg-blue-500/10 text-blue-400 border border-blue-500/20";
  }
};



onMounted(fetchUserSessions);
</script>
